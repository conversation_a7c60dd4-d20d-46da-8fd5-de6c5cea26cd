---
description: 
globs: 
alwaysApply: false
---
# Project Overview

This is a multi-component Natural Language Processing (NLP) project with several frontend implementations and backend services.

## Project Components

1. Frontend Applications:
   - `nlp-enclosure-frontend-react/`: Main React web application
   - `nlp-enclosure-frontend-react-mobile/`: Mobile-optimized React frontend
   - `nlp-enclosure-frontend-chrome/`: Chrome extension implementation
   - `nlp-enclosure-frontend-react-wps/`: WPS-specific React implementation

2. Backend Services:
   - `springboot-dify-api/`: Spring Boot backend service integrating with Dify
   - `load-dify-dateset/`: Data loading and processing service for Dify
   - `chatvoice/`: Voice chat related functionality

## Key Features
- Multiple frontend implementations for different platforms
- Spring Boot backend integration with Dify
- Dataset loading and management
- Voice chat capabilities

## Development Guidelines
- Each frontend implementation follows its own build and deployment process
- Backend services should be run independently
- Data loading should be performed before starting the main services
