---
description: 
globs: 
alwaysApply: false
---
# React Frontend Structure

The main React frontend application is located in `nlp-enclosure-frontend-react/`. This guide explains its structure and key conventions.

## Directory Structure
- `src/`: Source code directory
  - `components/`: Reusable React components
  - `pages/`: Page components and routing
  - `services/`: API and service integrations
  - `utils/`: Utility functions and helpers
  - `assets/`: Static assets (images, styles, etc.)

## Key Files
- `package.json`: Dependencies and scripts
- `src/App.js`: Main application component
- `src/index.js`: Application entry point

## Development Conventions
- Use functional components with hooks
- Follow React best practices for state management
- Maintain consistent component structure
- Keep components modular and reusable

## API Integration
- Backend communication through service layer
- Environment-based configuration
- Error handling and loading states
