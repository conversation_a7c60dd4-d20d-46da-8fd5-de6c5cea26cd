---
description: 
globs: 
alwaysApply: false
---
# Spring Boot Backend

The Spring Boot backend service (`springboot-dify-api/`) provides the API layer and integration with Dify services.

## Project Structure
- `src/main/java/`: Java source files
  - `controller/`: REST API endpoints
  - `service/`: Business logic layer
  - `model/`: Data models and entities
  - `repository/`: Data access layer
  - `config/`: Configuration classes
  - `util/`: Utility classes

## Key Features
- RESTful API endpoints
- Dify service integration
- Data processing and transformation
- Error handling and validation

## Configuration
- Application properties in `application.yml`
- Environment-specific configurations
- Dify API credentials management

## Development Guidelines
- Follow Spring Boot best practices
- Maintain clear separation of concerns
- Document API endpoints
- Handle errors gracefully
- Use proper logging

