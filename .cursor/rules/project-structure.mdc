---
description: 
globs: 
alwaysApply: false
---
# NLP智能对话平台项目结构

本工作区包含五个相互关联的项目，共同构成了一个完整的智能对话平台解决方案。

## 前端项目

### 1. 主要前端项目 - nlp-enclosure-frontend-react
- **项目定位**：大模型智能对话平台的核心前端项目
- **主要功能**：
  - 智能对话首页
  - Agent管理组件
  - 同时支持PC端和移动端适配
- **技术栈**：
  - 框架：React + TypeScript
  - UI组件库：[Ant Design](mdc:https:/ant.design) + [Ant Design X](mdc:https:/x.ant.design)

### 2. 移动端项目 - nlp-enclosure-frontend-react-mobile
- **项目定位**：企业微信H5应用版本
- **功能特点**：
  - 基于主项目精简，专注对话功能
  - 移除了Agent管理功能
  - 针对移动端场景优化
- **技术栈**：与主项目相同

### 3. WPS插件项目 - nlp-enclosure-frontend-react-wps
- **项目定位**：WPS办公软件插件版本
- **功能特点**：
  - 基于主项目定制
  - 专注对话功能
  - 移除了Agent管理功能
  - 适配WPS插件环境

## 后端项目

### 4. API服务 - springboot-dify-api
- **项目定位**：Dify接口的中间层服务
- **核心功能**：
  - Dify API的转发和封装
  - 用户认证和授权
- **技术架构**：
  - 框架：Spring Boot
  - 数据持久化：JPA
  - 缓存：Redis
  - 安全：JWT认证

## 语音服务

### 5. 语音转换服务 - chatvoice
- **项目定位**：智能语音转换服务
- **核心功能**：
  - 语音转文字（Speech-to-Text）
  - 文字转语音（Text-to-Speech）
  - WebSocket流式传输
- **技术特点**：
  - 集成CosyVoice2模型
  - 集成SenseVoice模型
  - 支持实时流式处理

## 项目间关系
- 前端项目（React）调用后端API服务
- 后端API服务与Dify平台交互
- 语音服务可以独立部署，通过API与其他服务集成

## 开发注意事项
1. 前端项目共享相似的技术栈，便于代码复用和维护
2. 移动端和WPS插件版本需要注意各自平台的特殊限制
3. 后端服务需要正确配置Dify的访问凭证
4. 语音服务需要确保模型文件正确加载和配置
