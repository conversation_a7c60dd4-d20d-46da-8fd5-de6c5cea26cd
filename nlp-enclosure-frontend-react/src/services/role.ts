import http from "../utils/request.ts";
import { Role } from "../types/role.ts";

/**
 * 获取所有用户组列表
 * @returns 用户组列表
 */
export const getRoleList = async (): Promise<Role[]> => {
    try {
      const response = await http.get('./enclosure/roles/getAllRoles');
      
      // 后端返回格式：{code: 200, data: roles[], msg: "获取用户组列表成功"}
      const result = JSON.parse(response);
      
      if (result.code !== 200) {
        console.error('获取用户组列表失败:', result.msg);
        return [];
      }

      return result.data || [];
    } catch (error) {
      console.error('Error fetching role list:', error);
      return [];
    }
};

/**
 * 搜索用户组
 * @param keyword 搜索关键词
 * @returns 匹配的用户组列表
 */
export const searchRoles = async (keyword: string): Promise<Role[]> => {
  try {
    // 先获取所有用户组，然后在前端进行过滤
    const allRoles = await getRoleList();
    
    if (!keyword) {
      return allRoles;
    }
    
    // 根据名称和描述进行模糊搜索
    return allRoles.filter(role => 
      role.name.toLowerCase().includes(keyword.toLowerCase()) ||
      (role.description && role.description.toLowerCase().includes(keyword.toLowerCase()))
    );
  } catch (error) {
    console.error('Error searching roles:', error);
    return [];
  }
};
