import React, { useEffect, useState } from 'react';
import { Modal, Switch, Select, Button, message, Spin } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { Role } from '../../types/role';
import { getRoleList, searchRoles } from '../../services/role';
import { getRoleVisibility, updateRoleVisibility } from '../../services/agentDict';
import debounce from 'lodash/debounce';

interface VisibilitySelectorProps {
  agentDictId: string;
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

/**
 * AgentDict可见性选择器组件
 * 包含全部可见开关和用户组多选下拉框
 */
const VisibilitySelector: React.FC<VisibilitySelectorProps> = ({
  agentDictId,
  open,
  onClose,
  onSuccess
}) => {
  // 状态管理
  const [isPublic, setIsPublic] = useState<boolean>(true);
  const [visibleRoleIds, setVisibleRoleIds] = useState<string[]>([]);
  const [roleOptions, setRoleOptions] = useState<Role[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);

  // 获取当前AgentDict的可见性设置
  useEffect(() => {
    if (open && agentDictId) {
      fetchVisibilitySetting();
      fetchRoleList();
    }
  }, [open, agentDictId]);

  // 获取可见性设置
  const fetchVisibilitySetting = async () => {
    try {
      setLoading(true);
      const result = await getRoleVisibility(agentDictId);
      if (result) {
        setIsPublic(result.isPublic);

        // 解析可见用户组ID列表
        if (result.visibleToRoles) {
          try {
            const roleIds = JSON.parse(result.visibleToRoles);
            setVisibleRoleIds(roleIds);
          } catch (e) {
            console.error('解析可见用户组ID列表失败:', e);
            setVisibleRoleIds([]);
          }
        } else {
          setVisibleRoleIds([]);
        }
      }
    } catch (error) {
      console.error('获取可见性设置失败:', error);
      message.error('获取可见性设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户组列表
  const fetchRoleList = async () => {
    try {
      setLoading(true);
      const response = await getRoleList();
      setRoleOptions(response || []);

      // 根据visibleRoleIds匹配用户组
      if (visibleRoleIds.length > 0 && response) {
        const selectedRoles = response.filter((role: Role) =>
          visibleRoleIds.includes(role.id)
        );
        setSelectedRoles(selectedRoles);
      }
    } catch (error) {
      console.error('获取用户组列表失败:', error);
      message.error('获取用户组列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索用户组
  const handleSearch = debounce(async (keyword: string) => {
    if (!keyword) {
      fetchRoleList();
      return;
    }

    try {
      setSearchLoading(true);
      const response = await searchRoles(keyword);
      setRoleOptions(response || []);
    } catch (error) {
      console.error('搜索用户组失败:', error);
    } finally {
      setSearchLoading(false);
    }
  }, 500);

  // 保存可见性设置
  const handleSave = async () => {
    try {
      setSaving(true);
      // 如果是公开可见，则不需要传递用户组ID列表
      // 如果不是公开可见，则传递用户组ID列表
      const response = await updateRoleVisibility(
        agentDictId,
        isPublic,
        isPublic ? [] : visibleRoleIds
      );

      if (response && response.code === 200) {
        message.success('保存成功');
        onSuccess?.();
        onClose();
      } else {
        message.error(response?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存可见性设置失败:', error);
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 处理用户组选择变化
  const handleRoleSelectChange = (values: string[]) => {
    setVisibleRoleIds(values);

    // 更新已选用户组列表
    const selected = roleOptions.filter(role => values.includes(role.id));
    setSelectedRoles(selected);
  };

  return (
    <Modal
      title="设置可见性"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={saving}
          onClick={handleSave}
        >
          保存
        </Button>
      ]}
      width={500}
    >
      <Spin spinning={loading}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <span style={{ marginRight: 8 }}>全部可见：</span>
            <Switch
              checked={isPublic}
              onChange={setIsPublic}
            />
          </div>

          {!isPublic && (
            <div>
              <div style={{ marginBottom: 8 }}>选择可见用户组：</div>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="请选择可见用户组"
                value={visibleRoleIds}
                onChange={handleRoleSelectChange}
                optionFilterProp="label"
                loading={searchLoading}
                onSearch={handleSearch}
                showSearch
                filterOption={false}
                options={roleOptions.map(role => ({
                  label: `${role.name}${role.description ? ` (${role.description})` : ''}`,
                  value: role.id
                }))}
                suffixIcon={<TeamOutlined />}
                notFoundContent={searchLoading ? <Spin size="small" /> : "无匹配用户组"}
                maxTagCount={5}
              />
            </div>
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default VisibilitySelector;
