package org.springblade.modules.repository;

import org.springblade.modules.entity.AgentDict;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 23:53
 */
@Repository
public interface AgentDictRepository extends JpaRepository<AgentDict, String> {

    AgentDict findByApiKey(String apiKey);

    @Query("SELECT a FROM AgentDict a WHERE a.apiKey = :apiKey AND a.apiServerHost = :apiServerHost AND a.isDeleted = false ORDER BY a.id ASC LIMIT 1")
    AgentDict findFirstByApiKeyAndApiServerHost(@Param("apiKey") String apiKey, @Param("apiServerHost") String apiServerHost);

    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = true")
    AgentDict getAgentDictByIsDefaultTrue();

    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false ORDER BY a.sortOrder ASC")
    List<AgentDict> findByIsDefaultFalseAndIsDeletedFalseOrderBySortOrderAsc();

    // 获取Order列的最大值
    @Query("SELECT MAX(a.sortOrder) FROM AgentDict a")
    Integer findOrderMax();

    /**
     * 查询公开可见的AgentDict列表
     */
    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false AND a.isPublic = true ORDER BY a.sortOrder ASC")
    List<AgentDict> findPublicAgentDicts();

    /**
     * 查询对特定用户可见的非公开AgentDict列表
     * 使用LIKE查询匹配JSON数组中的用户ID
     */
    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false AND a.isPublic = false AND (a.visibleToUsers LIKE %:userId% OR a.visibleToUsers LIKE %:userIdQuoted%) ORDER BY a.sortOrder ASC")
    List<AgentDict> findPrivateAgentDictsVisibleToUser(@Param("userId") String userId, @Param("userIdQuoted") String userIdQuoted);

    /**
     * 查询用户可见的所有AgentDict（包括公开的和对该用户可见的私有AgentDict）
     */
    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false AND (a.isPublic = true OR (a.isPublic = false AND (a.visibleToUsers LIKE %:userId% OR a.visibleToUsers LIKE %:userIdQuoted%))) ORDER BY a.sortOrder ASC")
    List<AgentDict> findAllAgentDictsVisibleToUser(@Param("userId") String userId, @Param("userIdQuoted") String userIdQuoted);

    /**
     * 查询基于用户组权限的AgentDict列表（新的权限控制方式）
     * 包括公开的和对用户所属用户组可见的私有AgentDict
     */
    @Query("SELECT DISTINCT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false AND " +
           "(a.isPublic = true OR " +
           "(a.isPublic = false AND (" +
           "a.visibleToRoles IS NULL OR a.visibleToRoles = '' OR " +
           "EXISTS (SELECT 1 FROM UserRole ur WHERE ur.userId = :userId AND " +
           "(LOWER(CAST(a.visibleToRoles as string)) LIKE LOWER(CONCAT('%\"', CAST(ur.roleId as string), '\"%')) OR " +
           "LOWER(CAST(a.visibleToRoles as string)) LIKE LOWER(CONCAT('%', CAST(ur.roleId as string), '%'))))" +
           "))) ORDER BY a.sortOrder ASC")
    List<AgentDict> findAllAgentDictsVisibleToUserByRoles(@Param("userId") String userId);

    /**
     * 查询对特定用户组可见的私有AgentDict列表
     */
    @Query("SELECT a FROM AgentDict a WHERE a.isDefault = false AND a.isDeleted = false AND a.isPublic = false AND " +
           "(a.visibleToRoles LIKE %:roleId% OR a.visibleToRoles LIKE %:roleIdQuoted%) ORDER BY a.sortOrder ASC")
    List<AgentDict> findPrivateAgentDictsVisibleToRole(@Param("roleId") String roleId, @Param("roleIdQuoted") String roleIdQuoted);
}
