package org.springblade.modules.security;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.gzyc.service.GzycUserService;
import org.springblade.gzyc.vo.GzycUserVO;
import org.springblade.modules.entity.Account;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springblade.qyweixin.security.CustomUserDetails;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

/**
 * 用户名密码登录的UserDetailsService实现
 * 固定用户名和密码为admin/admin
 */
@Slf4j
@Component
public class UsernamePasswordUserDetailsService implements UserDetailsService {

//    @Resource
//    private GzycUserService gzycUserService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("尝试使用用户名密码登录: {}", username);

//        GzycUserVO userInfo = gzycUserService.getUserInfo("123");
        
        // 固定用户名和密码为admin/admin
        if ("admin".equals(username)) {
            return createAdminUserDetails();
        }
        
        if ("wps".equals(username)) {
            return createWpsUserDetails();
        }
        
        throw new UsernameNotFoundException("用户名或密码不正确");
    }

    private CustomUserDetails createWpsUserDetails() {
        // 创建一个管理员账户对象
        Account wpsAccount = Account.builder()
                .id("292aceb9-e5da-438e-bcaf-36062767e27a")
                .name("gzyc")
                .email("<EMAIL>")
                .password(new BCryptPasswordEncoder().encode("wps"))
                .status("active")
                .build();

        // 设置权限
        Set<GrantedAuthority> authorities = new LinkedHashSet<>();
//        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        wpsAccount.setWorkNo("667788");

        return new CustomUserDetails(wpsAccount, authorities);
    }

    private CustomUserDetails createAdminUserDetails() {
        // 创建一个管理员账户对象
        Account adminAccount = Account.builder()
                .id("292aceb9-e5da-438e-bcaf-36062767e27a")
                .name("gzyc")
                .email("<EMAIL>")
                .password(new BCryptPasswordEncoder().encode("admin"))
                .status("active")
                .build();
        
        // 设置权限
        Set<GrantedAuthority> authorities = new LinkedHashSet<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        adminAccount.setWorkNo("****************");
        
        return new CustomUserDetails(adminAccount, authorities);
    }
}